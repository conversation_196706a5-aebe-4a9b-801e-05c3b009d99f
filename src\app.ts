import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import config from '@/config/config';
import { logger } from '@/utils/logger';
import { globalErrorHandler, notFoundHandler } from '@/middlewares/error.middleware';

// Create Express application
const createApp = (): Application => {
  const app = express();

  // Trust proxy (important for rate limiting and getting real IP)
  app.set('trust proxy', 1);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // CORS configuration
  app.use(cors({
    origin: config.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // Rate limiting
  const limiter = rateLimit({
    windowMs: config.RATE_LIMIT_WINDOW_MS,
    max: config.RATE_LIMIT_MAX_REQUESTS,
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Logging middleware
  if (config.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined', {
      stream: {
        write: (message: string) => {
          logger.http(message.trim());
        },
      },
    }));
  }

  return app;
};

// Setup routes
const setupRoutes = (app: Application): void => {
  // Health check endpoint
  app.get('/health', (req: Request, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'Server is running',
      timestamp: new Date().toISOString(),
      environment: config.NODE_ENV,
    });
  });

  // API routes
  app.get('/api', (req: Request, res: Response) => {
    res.status(200).json({
      success: true,
      message: 'RiteFit.AI Backend API',
      version: '1.0.0',
      documentation: '/api/docs',
    });
  });

  // TODO: Add module routes here
  // Example:
  // app.use('/api/auth', authRoutes);
  // app.use('/api/users', userRoutes);

  // 404 handler for undefined routes
  app.all('*', notFoundHandler);

  // Global error handling middleware
  app.use(globalErrorHandler);
};

// Create and configure the Express application
export const createExpressApp = (): Application => {
  const app = createApp();
  setupRoutes(app);
  
  logger.info('Express application created and configured');
  return app;
};

export default createExpressApp;

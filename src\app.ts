import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import config from '@/config/config';
import { logger } from '@/utils/logger';
import { globalErrorHandler, notFoundHandler } from '@/middlewares/error.middleware';
import { setupRoutes as setupAppRoutes, logRegisteredRoutes } from '@/router';

// Create Express application
const createApp = (): Application => {
  const app = express();

  // Trust proxy (important for rate limiting and getting real IP)
  app.set('trust proxy', 1);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // CORS configuration
  app.use(cors({
    origin: config.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // TODO: Add rate limiting
  // const limiter = rateLimit({
  //   windowMs: config.RATE_LIMIT_WINDOW_MS,
  //   max: config.RATE_LIMIT_MAX_REQUESTS,
  //   message: {
  //     success: false,
  //     message: 'Too many requests from this IP, please try again later.',
  //   },
  //   standardHeaders: true,
  //   legacyHeaders: false,
  // });
  // app.use('/api/', limiter);

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Logging middleware
  if (config.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined', {
      stream: {
        write: (message: string) => {
          logger.http(message.trim());
        },
      },
    }));
  }

  return app;
};

// Setup routes and error handling
const setupAppMiddleware = (app: Application): void => {
  // Setup all routes using the router
  setupAppRoutes(app);

  // 404 handler for undefined routes
  app.all('*', notFoundHandler);

  // Global error handling middleware
  app.use(globalErrorHandler);
};

// Create and configure the Express application
export const createExpressApp = (): Application => {
  const app = createApp();
  setupAppMiddleware(app);

  logger.info('Express application created and configured');
  return app;
};

export default createExpressApp;

// Example usage of transaction wrapper functions
import { withTransaction, withOptionalTransaction, tryCatch } from './transaction';
import { Prisma } from '@prisma/client';

// Example 1: Using withTransaction for operations that need a transaction
export const createUserWithProfile = async (userData: any, profileData: any) => {
  return withTransaction(async (tx) => {
    // Create user
    const user = await tx.user.create({
      data: userData,
    });

    // Create profile for the user
    const profile = await tx.profile.create({
      data: {
        ...profileData,
        userId: user.id,
      },
    });

    return { user, profile };
  });
};

// Example 2: Using withOptionalTransaction for flexible operations
export const updateUser = async (
  userId: string,
  userData: any,
  transaction?: Prisma.TransactionClient
) => {
  return withOptionalTransaction(
    async (client) => {
      return await client.user.update({
        where: { id: userId },
        data: userData,
      });
    },
    transaction
  );
};

// Example 3: Using tryCatch for simple operations
export const getUserById = async (userId: string) => {
  return tryCatch(
    async () => {
      const user = await db.user.findUnique({
        where: { id: userId },
        include: { profile: true },
      });
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    },
    `Failed to get user with ID: ${userId}`
  );
};

// Example 4: Complex operation using multiple wrapper functions
export const transferUserData = async (fromUserId: string, toUserId: string) => {
  return withTransaction(async (tx) => {
    // Get source user data
    const fromUser = await withOptionalTransaction(
      async (client) => {
        return await client.user.findUnique({
          where: { id: fromUserId },
          include: { profile: true },
        });
      },
      tx
    );

    if (!fromUser) {
      throw new Error('Source user not found');
    }

    // Update target user
    const updatedUser = await withOptionalTransaction(
      async (client) => {
        return await client.user.update({
          where: { id: toUserId },
          data: {
            // Transfer some data
            firstName: fromUser.firstName,
            lastName: fromUser.lastName,
          },
        });
      },
      tx
    );

    // Delete source user
    await withOptionalTransaction(
      async (client) => {
        await client.user.delete({
          where: { id: fromUserId },
        });
      },
      tx
    );

    return updatedUser;
  });
};

import { Router, Application } from 'express';

// Import route modules here
// TODO: Import actual route modules when they are created
// import { authRoutes } from '@/modules/core/auth/auth.route';
// import { userRoutes } from '@/modules/core/user/user.route';
// import { notificationRoutes } from '@/modules/support/notification/notification.route';

// Create main API router
const createApiRouter = (): Router => {
  const apiRouter = Router();

  // Health check for API
  apiRouter.get('/', (req, res) => {
    res.status(200).json({
      success: true,
      message: 'RiteFit.AI Backend API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        users: '/api/users',
        notifications: '/api/notifications',
      },
    });
  });

  // TODO: Register module routes here
  // Core module routes
  // apiRouter.use('/auth', authRoutes);
  // apiRouter.use('/users', userRoutes);
  
  // Support module routes
  // apiRouter.use('/notifications', notificationRoutes);

  logger.info('API routes registered successfully');
  return apiRouter;
};

// Setup all application routes
export const setupRoutes = (app: Application): void => {
  // Global health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      success: true,
      message: 'Server is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
    });
  });

  // API routes
  const apiRouter = createApiRouter();
  app.use('/api', apiRouter);

};

// Function to get all registered routes (useful for debugging)
export const getRegisteredRoutes = (app: Application): string[] => {
  const routes: string[] = [];
  
  const extractRoutes = (stack: any[], prefix = '') => {
    stack.forEach((layer) => {
      if (layer.route) {
        // Direct route
        const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
        routes.push(`${methods} ${prefix}${layer.route.path}`);
      } else if (layer.name === 'router') {
        // Router middleware
        const routerPrefix = layer.regexp.source
          .replace('\\/?', '')
          .replace('(?=\\/|$)', '')
          .replace(/\\\//g, '/')
          .replace(/\$.*/, '');
        
        if (layer.handle.stack) {
          extractRoutes(layer.handle.stack, prefix + routerPrefix);
        }
      }
    });
  };

  if (app._router && app._router.stack) {
    extractRoutes(app._router.stack);
  }

  return routes;
};

// Function to log all registered routes
export const logRegisteredRoutes = (app: Application): void => {
  const routes = getRegisteredRoutes(app);
  logger.info('Registered routes:');
  routes.forEach(route => {
    logger.info(`  ${route}`);
  });
};

export default setupRoutes;

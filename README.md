# RiteFit.AI Backend

A scalable backend API built with Node.js, Express.js, Prisma ORM, and MySQL, focusing on code readability and maintainability using function-based architecture.

## 🏗️ Architecture

This project follows a **module-based folder structure** with **core/support classification** and uses **functions instead of classes** for better simplicity and maintainability.

### Folder Structure

```
src/
├── modules/
│   ├── core/           # Business-critical modules
│   │   ├── auth/       # Authentication & authorization
│   │   └── user/       # User management
│   └── support/        # Auxiliary modules
│       └── notification/ # Notifications system
├── middlewares/        # Global middlewares
├── config/            # Configuration files
├── utils/             # Utility helpers
├── services/          # Cross-module shared services
├── router/            # Route management
├── app.ts             # Express app setup
└── server.ts          # Entry point
```

### Module Structure

Each module follows this structure:
```
module-name/
├── models/            # One file per entity/table
├── dto/               # Data Transfer Objects
│   ├── *.request.dto.ts
│   ├── *.response.dto.ts
│   └── *.service.dto.ts
├── {module}.service.ts    # Business logic
├── {module}.controller.ts # Route handlers
├── {module}.route.ts      # Express Router config
└── {module}.validator.ts  # Custom validations
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ritefit-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your database credentials and other configurations.

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push schema to database
   npm run db:push
   
   # Or run migrations (recommended for production)
   npm run db:migrate
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

## 📜 Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run dev:ts` - Start development server with TypeScript
- `npm run build` - Build TypeScript to JavaScript
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## 🛠️ Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **ORM**: Prisma
- **Language**: TypeScript
- **Validation**: Zod
- **Authentication**: JWT
- **Security**: Helmet, CORS
- **Logging**: Custom logger utility
- **Code Quality**: ESLint, Prettier

## 🔧 Configuration

The application uses environment variables for configuration. Key variables include:

- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port
- `DATABASE_URL` - MySQL connection string
- `JWT_SECRET` - JWT signing secret
- `CORS_ORIGIN` - Allowed CORS origins

## 🏃‍♂️ Development Guidelines

### Function-Based Architecture
- Use functions instead of classes
- Keep functions pure when possible
- Use functional composition

### Module Organization
- Place business-critical modules in `core/`
- Place auxiliary modules in `support/`
- One model file per database table
- Keep business logic in service files

### Naming Conventions
- **Folders**: kebab-case
- **Files**: `{module}.{type}.ts`
- **Functions**: camelCase
- **Types/Interfaces**: PascalCase

## 🔒 Security Features

- Helmet for security headers
- CORS configuration
- JWT authentication
- Input validation with Zod
- Rate limiting (configurable)
- Error handling middleware

## 📊 API Endpoints

- `GET /health` - Health check
- `GET /api` - API information
- `POST /api/auth/*` - Authentication endpoints (to be implemented)
- `GET /api/users/*` - User management endpoints (to be implemented)

## 🤝 Contributing

1. Follow the established folder structure
2. Use functions instead of classes
3. Write TypeScript with proper types
4. Add proper validation using Zod
5. Include error handling
6. Write meaningful commit messages

## 📝 License

This project is licensed under the ISC License.

{"name": "ritefit-backend", "version": "1.0.0", "description": "Scalable backend API for RiteFit.AI using Node.js, Express.js, Prisma, and MySQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:ts": "nodemon --exec ts-node src/server.ts", "build": "tsc", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "prisma", "mysql", "api", "backend"], "author": "RiteFit.AI Team", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "prisma": "^6.9.0"}, "devDependencies": {"@types/node": "^22.15.30", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
import { Request, Response } from 'express';
import { PrismaClient, Prisma } from '@prisma/client';
import { db } from '@/config/db.config';

// Controller wrapper function
export const executeController = async (
  needTransaction: boolean,
  controllerFunction: (request: Request, transaction?: Prisma.TransactionClient) => Promise<any>,
  request: Request,
  response: Response,
  prismaClient: PrismaClient = db
): Promise<void> => {
  try {
    let result;

    if (needTransaction) {
      // Execute controller with transaction
      result = await prismaClient.$transaction(async (transaction) => {
        return await controllerFunction(request, transaction);
      });
    } else {
      // Execute controller without transaction
      result = await controllerFunction(request);
    }

    // Send success response
    response.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Controller execution failed:', error);
    
    // Send error response
    response.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Something went wrong',
    });
  }
};

import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { db } from '@/config/db.config';

// Controller types
type ReadController = (req: Request) => Promise<any>;
type WriteController = (req: Request, tx: Prisma.TransactionClient) => Promise<any>;

// Wrapper options interfaces
interface BaseWrapperOptions {
  maxWait?: number;
  timeout?: number;
  successMessage?: string;
}

interface ReadWrapperOptions extends BaseWrapperOptions {
  needsTransaction: false;
}

interface WriteWrapperOptions extends BaseWrapperOptions {
  needsTransaction: true;
}

type WrapperOptions = ReadWrapperOptions | WriteWrapperOptions;

// Response utility functions
const ResponseUtil = {
  success: (res: Response, message: string, result?: any) => {
    res.status(200).json({
      success: true,
      message,
      data: result?.data,
      paggination: result?.pagination,
    });
  },
};

// Controller wrapper function overloads
export function controllerWrapper(
  controller: ReadController,
  options: ReadWrapperOptions
): (req: Request, res: Response, next: NextFunction) => Promise<void>;

export function controllerWrapper(
  controller: WriteController,
  options: WriteWrapperOptions
): (req: Request, res: Response, next: NextFunction) => Promise<void>;

export function controllerWrapper(
  controller: ReadController | WriteController,
  options: WrapperOptions
) {
  const {
    needsTransaction,
    maxWait = 5000,
    timeout = 10000,
    successMessage = 'Operation successful'
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      let result;

      if (!needsTransaction) {
        result = await (controller as ReadController)(req);
      } else {
        result = await db.$transaction(
          (tx: Prisma.TransactionClient) => (controller as WriteController)(req, tx),
          { maxWait, timeout }
        );
      }

      ResponseUtil.success(res, successMessage, result);
    } catch (error) {
      next(error);
    }
  };
}

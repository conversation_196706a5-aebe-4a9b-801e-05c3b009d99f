// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and user management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  firstName String?
  lastName  String?
  password  String
  isActive  Boolean  @default(true)
  role      UserRole @default(USER)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  refreshTokens RefreshToken[]
  
  @@map("users")
}

// Refresh token model for JWT token management
model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  isRevoked Boolean  @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

// Enum for user roles
enum UserRole {
  ADMIN
  USER
  MODERATOR
}

// Example model for demonstration - you can modify based on your needs
model Profile {
  id     String  @id @default(cuid())
  bio    String?
  avatar String?
  userId String  @unique
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("profiles")
}

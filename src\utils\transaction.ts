import { PrismaClient, Prisma } from '@prisma/client';
import { db } from '@/config/db.config';

// Transaction wrapper function
export const withTransaction = async <T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>,
  prisma: PrismaClient = db
): Promise<T> => {
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await operation(tx);
    });
    return result;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Wrapper function that can optionally use transaction
export const withOptionalTransaction = async <T>(
  operation: (tx: PrismaClient | Prisma.TransactionClient) => Promise<T>,
  transaction?: Prisma.TransactionClient,
  prisma: PrismaClient = db
): Promise<T> => {
  try {
    // If transaction is provided, use it; otherwise use the regular prisma client
    const client = transaction || prisma;
    const result = await operation(client);
    return result;
  } catch (error) {
    console.error('Operation failed:', error);
    throw error;
  }
};

// Simple try-catch wrapper for any async operation
export const tryCatch = async <T>(
  operation: () => Promise<T>,
  errorMessage?: string
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    console.error(errorMessage || 'Operation failed:', error);
    throw error;
  }
};

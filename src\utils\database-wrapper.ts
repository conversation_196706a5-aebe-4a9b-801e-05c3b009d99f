import { Request } from 'express';
import { PrismaClient, Prisma } from '@prisma/client';
import { db } from '@/config/db.config';

// Database operation wrapper function
export const executeWithDatabase = async <T>(
  needTransaction: boolean,
  operation: (req: Request, transaction?: Prisma.TransactionClient) => Promise<T>,
  request: Request,
  prismaClient: PrismaClient = db
): Promise<T> => {
  try {
    if (needTransaction) {
      // Execute with transaction
      return await prismaClient.$transaction(async (transaction) => {
        return await operation(request, transaction);
      });
    } else {
      // Execute without transaction
      return await operation(request);
    }
  } catch (error) {
    console.error('Database operation failed:', error);
    throw error;
  }
};
